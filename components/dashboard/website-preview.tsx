"use client"

import { useState, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Download, 
  Code, 
  Eye, 
  Save, 
  ExternalLink,
  Smartphone,
  Monitor,
  Tablet
} from "lucide-react"
import J<PERSON><PERSON><PERSON> from "jszip"
import { downloadFile } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"

interface WebsitePreviewProps {
  htmlCode: string
  title?: string
  onSave?: () => void
  onEdit?: () => void
}

export function WebsitePreview({ htmlCode, title, onSave, onEdit }: WebsitePreviewProps) {
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const { toast } = useToast()

  const handleDownload = async () => {
    try {
      const zip = new JSZip()
      
      // Add HTML file
      zip.file("index.html", htmlCode)
      
      // Add a simple README
      const readme = `# ${title || "Generated Website"}

This website was generated using WebGenie AI.

## Files included:
- index.html - The main HTML file

## How to use:
1. Open index.html in your web browser
2. Upload to your web hosting service
3. Customize as needed

Generated with ❤️ by WebGenie
`
      zip.file("README.md", readme)
      
      // Generate and download
      const content = await zip.generateAsync({ type: "blob" })
      const url = URL.createObjectURL(content)
      const link = document.createElement("a")
      link.href = url
      link.download = `${title?.replace(/[^a-z0-9]/gi, "_") || "website"}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Download started",
        description: "Your website has been packaged and downloaded.",
      })
    } catch (error) {
      toast({
        title: "Download failed",
        description: "There was an error creating the download package.",
        variant: "destructive",
      })
    }
  }

  const handleOpenInNewTab = () => {
    const newWindow = window.open()
    if (newWindow) {
      newWindow.document.write(htmlCode)
      newWindow.document.close()
    }
  }

  const getIframeWidth = () => {
    switch (viewMode) {
      case "mobile":
        return "375px"
      case "tablet":
        return "768px"
      default:
        return "100%"
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-green-500" />
              Website Preview
            </CardTitle>
            <div className="flex items-center gap-2">
              {/* View Mode Toggles */}
              <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
                <Button
                  variant={viewMode === "desktop" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("desktop")}
                >
                  <Monitor className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "tablet" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("tablet")}
                >
                  <Tablet className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "mobile" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("mobile")}
                >
                  <Smartphone className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2">
              {onEdit && (
                <Button variant="outline" onClick={onEdit}>
                  <Code className="w-4 h-4 mr-2" />
                  Edit Code
                </Button>
              )}
              {onSave && (
                <Button variant="outline" onClick={onSave}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Project
                </Button>
              )}
              <Button variant="outline" onClick={handleOpenInNewTab}>
                <ExternalLink className="w-4 h-4 mr-2" />
                Open in New Tab
              </Button>
              <Button onClick={handleDownload}>
                <Download className="w-4 h-4 mr-2" />
                Download ZIP
              </Button>
            </div>

            {/* Preview Frame */}
            <div className="border rounded-lg overflow-hidden bg-white">
              <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 border-b">
                <div className="flex gap-1">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="text-sm text-gray-600 ml-2">
                  {title || "Generated Website"}
                </div>
              </div>
              <div className="flex justify-center bg-gray-50 p-4">
                <div 
                  className="transition-all duration-300 border bg-white shadow-lg"
                  style={{ 
                    width: getIframeWidth(),
                    maxWidth: "100%"
                  }}
                >
                  <iframe
                    ref={iframeRef}
                    srcDoc={htmlCode}
                    className="w-full border-0"
                    style={{ 
                      height: "600px",
                      minHeight: "400px"
                    }}
                    title="Website Preview"
                    sandbox="allow-scripts allow-same-origin"
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
