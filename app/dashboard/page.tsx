"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { motion } from "framer-motion"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { PromptForm } from "@/components/dashboard/prompt-form"
import { WebsitePreview } from "@/components/dashboard/website-preview"
import { RecentProjects } from "@/components/dashboard/recent-projects"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Sparkles, Zap, Code, Download } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface GeneratedWebsite {
  id: string
  htmlCode: string
  title: string
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedWebsite, setGeneratedWebsite] = useState<GeneratedWebsite | null>(null)
  const { toast } = useToast()

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/login")
  }

  const handleGenerate = async (prompt: string) => {
    setIsGenerating(true)
    try {
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to generate website")
      }

      const data = await response.json()
      setGeneratedWebsite(data)
      
      toast({
        title: "Website generated!",
        description: "Your website has been created successfully.",
      })
    } catch (error) {
      console.error("Generation error:", error)
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSave = async () => {
    if (!generatedWebsite) return

    try {
      const response = await fetch("/api/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: generatedWebsite.title,
          prompt: "Generated from dashboard",
          htmlCode: generatedWebsite.htmlCode,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to save project")
      }

      toast({
        title: "Project saved!",
        description: "Your website has been saved to your projects.",
      })
    } catch (error) {
      toast({
        title: "Save failed",
        description: "Failed to save your project. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardHeader />
      
      <main className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold mb-2">
            Welcome back, {session?.user?.name?.split(" ")[0] || "Creator"}! 👋
          </h1>
          <p className="text-lg text-muted-foreground">
            Ready to create something amazing? Describe your website and let AI build it for you.
          </p>
        </motion.div>

        {!generatedWebsite ? (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2">
              <PromptForm onGenerate={handleGenerate} isLoading={isGenerating} />
            </div>

            {/* Stats/Info Sidebar */}
            <div className="space-y-6">
              <RecentProjects />

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="w-5 h-5 text-yellow-500" />
                      Quick Stats
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Websites Generated</span>
                      <span className="font-semibold">0</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Projects Saved</span>
                      <span className="font-semibold">0</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Downloads</span>
                      <span className="font-semibold">0</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5 text-blue-500" />
                      Features
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Code className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Clean, semantic HTML</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Download className="w-4 h-4 text-purple-500" />
                      <span className="text-sm">Export as ZIP</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Sparkles className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm">AI-powered generation</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <WebsitePreview
              htmlCode={generatedWebsite.htmlCode}
              title={generatedWebsite.title}
              onSave={handleSave}
              onEdit={() => {
                // TODO: Implement edit functionality
                toast({
                  title: "Coming soon",
                  description: "Code editing feature will be available soon!",
                })
              }}
            />
            
            <div className="flex justify-center">
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                onClick={() => setGeneratedWebsite(null)}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Generate Another Website
              </motion.button>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
