"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { motion } from "framer-motion"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { PromptForm } from "@/components/dashboard/prompt-form"
import { WebsitePreview } from "@/components/dashboard/website-preview"
import { useToast } from "@/hooks/use-toast"

interface GeneratedWebsite {
  id: string
  htmlCode: string
  title: string
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedWebsite, setGeneratedWebsite] = useState<GeneratedWebsite | null>(null)
  const { toast } = useToast()

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/login")
  }

  const handleGenerate = async (prompt: string) => {
    setIsGenerating(true)
    try {
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to generate website")
      }

      const data = await response.json()
      setGeneratedWebsite(data)
      
      toast({
        title: "Website generated!",
        description: "Your website has been created successfully.",
      })
    } catch (error) {
      console.error("Generation error:", error)
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSave = async () => {
    if (!generatedWebsite) return

    // For now, just download the HTML file
    const blob = new Blob([generatedWebsite.htmlCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${generatedWebsite.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Website downloaded!",
      description: "Your HTML file has been saved to your downloads folder.",
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardHeader />
      
      <main className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold mb-2">
            Welcome back, {session?.user?.name?.split(" ")[0] || "Creator"}! 👋
          </h1>
          <p className="text-lg text-muted-foreground">
            Ready to create something amazing? Describe your website and let AI build it for you.
          </p>
        </motion.div>

        {!generatedWebsite ? (
          <div className="max-w-4xl mx-auto">
            <PromptForm onGenerate={handleGenerate} isLoading={isGenerating} />
          </div>
        ) : (
          <div className="space-y-8">
            <WebsitePreview
              htmlCode={generatedWebsite.htmlCode}
              title={generatedWebsite.title}
              onSave={handleSave}
              onEdit={() => {
                // TODO: Implement edit functionality
                toast({
                  title: "Coming soon",
                  description: "Code editing feature will be available soon!",
                })
              }}
            />
            
            <div className="flex justify-center">
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                onClick={() => setGeneratedWebsite(null)}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Generate Another Website
              </motion.button>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
